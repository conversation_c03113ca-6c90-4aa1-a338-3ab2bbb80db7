import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { TokenUsage, TokenType } from './token-usage.entity';

type LLMProvider = 'OPENAI' | 'GEMINI';

@Injectable()
export class LLMService {
  private provider: LLMProvider;
  private openai: OpenAI;
  private genAI: GoogleGenerativeAI;
  private readonly logger = new Logger(LLMService.name);

  constructor(
    private configService: ConfigService,
    @InjectRepository(TokenUsage)
    private tokenUsageRepository: Repository<TokenUsage>
  ) {
    this.provider = this.configService.get<LLMProvider>('LLM') || 'OPENAI';
    
    if (this.provider === 'OPENAI') {
      this.openai = new OpenAI({
        apiKey: this.configService.get<string>('OPENAI_API_KEY'),
      });
    } else {
      this.genAI = new GoogleGenerativeAI(
        this.configService.get<string>('GEMINI_API_KEY')
      );
    }
  }

  private async retryOperation<T>(operation: () => Promise<T>, maxRetries = 3): Promise<T> {
    let lastError: any;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        this.logger.warn(`Attempt ${attempt} failed: ${error.message}`);
        await new Promise(res => setTimeout(res, 1000 * Math.pow(2, attempt - 1))); // exponential backoff
      }
    }
    throw lastError;
  }  

  private async recordTokenUsage(
    embeddingId: string | string[],
    inputTokens: number,
    outputTokens: number = 0
  ) {
    if (Array.isArray(embeddingId)) {
      const tokensPerItem = Math.floor(inputTokens / embeddingId.length);
      await this.tokenUsageRepository.save(
        embeddingId.map(id => ({
          embeddingId: id,
          tokenType: TokenType.EMBEDDING_INPUT,
          tokensUsed: tokensPerItem
        }))
      );
    } else {
      await this.tokenUsageRepository.save([
        {
          embeddingId,
          tokenType: TokenType.COMPLETION_INPUT,
          tokensUsed: inputTokens
        },
        ...(outputTokens > 0 ? [{
          embeddingId,
          tokenType: TokenType.COMPLETION_OUTPUT,
          tokensUsed: outputTokens
        }] : [])
      ]);
    }
  }

  async createEmbedding(text: string, embeddingId: string): Promise<number[]> {
    if (this.provider === 'OPENAI') {
      const response = await this.openai.embeddings.create({
        model: "text-embedding-3-small",
        input: text,
        encoding_format: "float",
      });

      await this.recordTokenUsage(embeddingId, response.usage.total_tokens);
      return response.data[0].embedding;
    } else {
      const model = this.genAI.getGenerativeModel({ model: "embedding-001" });
      const result = await model.embedContent(text);
      
      // Estimate tokens (Gemini doesn't provide token counts)
      const estimatedTokens = Math.ceil(text.length / 4); // Rough approximation
      await this.recordTokenUsage(embeddingId, estimatedTokens);
      
      return result.embedding.values;
    }
  }

  async createEmbeddings(texts: string[], embeddingIds: string[]): Promise<number[][]> {
    if (this.provider === 'OPENAI') {
      const response = await this.openai.embeddings.create({
        model: "text-embedding-3-small",
        input: texts,
        encoding_format: "float",
      });

      const tokensPerText = Math.floor(response.usage.total_tokens / texts.length);
      await this.recordTokenUsage(embeddingIds, tokensPerText);
      return response.data.map(item => item.embedding);
    } else {
      const model = this.genAI.getGenerativeModel({ model: "embedding-001" });
      const embeddings: number[][] = [];
      
      for (const text of texts) {
        const result = await model.embedContent(text);
        embeddings.push(result.embedding.values);
      }

      // Estimate tokens for all texts
      const totalTokens = texts.reduce((sum, text) => sum + Math.ceil(text.length / 4), 0);
      const tokensPerText = Math.floor(totalTokens / texts.length);
      await this.recordTokenUsage(embeddingIds, tokensPerText);
      
      return embeddings;
    }
  }

  /**
   * Safely parse JSON with improved error handling and recovery attempts
   */
  private safeJsonParse(jsonString: string): any {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      this.logger.warn(`JSON parsing error: ${error.message}. Attempting to fix the JSON...`);
      
      // Try to extract JSON if surrounded by markdown or other text
      const jsonMatch = jsonString.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch (e) {
          // Still failed, continue to other recovery methods
        }
      }
      
      // Try to extract JSON with more aggressive pattern matching
      const jsonRegex = /(\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\})/g;
      const matches = jsonString.match(jsonRegex);
      if (matches && matches.length > 0) {
        for (const potentialJson of matches) {
          try {
            return JSON.parse(potentialJson);
          } catch (e) {
            // Try the next match
          }
        }
      }
      
      // Fix common JSON syntax errors
      let fixedJson = jsonString;
      
      // Remove markdown formatting
      fixedJson = fixedJson.replace(/\*\*/g, '');
      fixedJson = fixedJson.replace(/```json|```/g, '');
      
      // Fix trailing commas in arrays and objects
      fixedJson = fixedJson.replace(/,(\s*[\]}])/g, '$1');
      
      // Fix missing commas between array elements
      fixedJson = fixedJson.replace(/}(\s*){/g, '},$1{');
      fixedJson = fixedJson.replace(/"(\s*){/g, '",$1{');
      
      // Fix unquoted property names
      fixedJson = fixedJson.replace(/(\s*)(\w+)(\s*):(\s*)/g, '$1"$2"$3:$4');
      
      try {
        return JSON.parse(fixedJson);
      } catch (e) {
        this.logger.error(`Failed to fix JSON: ${e.message}`);
        
        // As a last resort, create a minimal valid structure
        return { 
          testCases: [{ 
            name: "Error parsing LLM response",
            precondition: "JSON parsing failed",
            steps: ["See error logs for details"],
            expectation: "Valid JSON response",
            testType: "error",
            priority: "high",
            platform: "system",
            testCaseType: "manual",
            automationByAgentq: false
          }]
        };
      }
    }
  }

  // Helper method to truncate content if it's too large
  private truncateContent(content: string, maxLength: number = 15000): string {
    if (content.length <= maxLength) {
      return content;
    }

    // Try to find a good breaking point (end of a section or endpoint)
    const truncated = content.substring(0, maxLength);
    const lastEndpoint = truncated.lastIndexOf('\n\nGET ') ||
                         truncated.lastIndexOf('\n\nPOST ') ||
                         truncated.lastIndexOf('\n\nPUT ') ||
                         truncated.lastIndexOf('\n\nDELETE ') ||
                         truncated.lastIndexOf('\n\nPATCH ');

    if (lastEndpoint > maxLength * 0.7) {
      return truncated.substring(0, lastEndpoint) + '\n\n[Content truncated for processing...]';
    }

    return truncated + '\n\n[Content truncated for processing...]';
  }

  // Helper method to extract key API information for test case generation
  private extractKeyApiInfo(content: string): string {
    const lines = content.split('\n');
    const keyInfo: string[] = [];

    // Extract API title and description
    const apiMatch = content.match(/API: (.+)/);
    if (apiMatch) keyInfo.push(`API: ${apiMatch[1]}`);

    const descMatch = content.match(/Description: (.+)/);
    if (descMatch) keyInfo.push(`Description: ${descMatch[1]}`);

    // Extract security information
    const securitySection = content.match(/Security:\n([\s\S]*?)\n\nAPI Endpoints:/);
    if (securitySection) {
      keyInfo.push('Security:');
      keyInfo.push(securitySection[1]);
    }

    // Extract endpoints with their key information
    const endpointRegex = /(GET|POST|PUT|DELETE|PATCH) ([^\n]+)\n([\s\S]*?)(?=\n(?:GET|POST|PUT|DELETE|PATCH) |$)/g;
    let match: RegExpExecArray | null;
    let endpointCount = 0;

    keyInfo.push('\nKey API Endpoints:');

    while ((match = endpointRegex.exec(content)) && endpointCount < 10) {
      const [, method, path, details] = match;
      keyInfo.push(`\n${method} ${path}`);

      // Extract summary, description, parameters, and responses
      const summaryMatch = details.match(/Summary: (.+)/);
      if (summaryMatch) keyInfo.push(`  Summary: ${summaryMatch[1]}`);

      const descriptionMatch = details.match(/Description: (.+)/);
      if (descriptionMatch) keyInfo.push(`  Description: ${descriptionMatch[1]}`);

      // Extract parameters
      const paramSection = details.match(/Parameters:\n([\s\S]*?)(?=\n  [A-Z]|$)/);
      if (paramSection) {
        keyInfo.push('  Parameters:');
        const paramLines = paramSection[1].split('\n').slice(0, 5); // Limit to 5 parameters
        keyInfo.push(...paramLines);
      }

      // Extract key responses
      const responseSection = details.match(/Responses:\n([\s\S]*?)(?=\n[A-Z]|$)/);
      if (responseSection) {
        keyInfo.push('  Key Responses:');
        const responseLines = responseSection[1].split('\n').slice(0, 3); // Limit to 3 responses
        keyInfo.push(...responseLines);
      }

      endpointCount++;
    }

    // Add schema information if available
    const schemaSection = content.match(/Data Models and Schemas:\n([\s\S]*?)$/);
    if (schemaSection) {
      keyInfo.push('\nKey Data Models:');
      const schemaLines = schemaSection[1].split('\n').slice(0, 20); // Limit schema info
      keyInfo.push(...schemaLines);
    }

    return keyInfo.join('\n');
  }

  async generateTestCases(content: string, embeddingId: string) {
    // Process content to make it suitable for LLM
    const processedContent = this.extractKeyApiInfo(content);
    const finalContent = this.truncateContent(processedContent, 12000);

    this.logger.debug(`Content length: original=${content.length}, processed=${processedContent.length}, final=${finalContent.length}`);

    // Enhanced prompt with stronger emphasis on JSON format and specific API testing
    const prompt = `
      As a QA expert specializing in API testing, analyze the following API documentation and generate comprehensive test cases.

      For each test case, provide:
      1. A clear, descriptive name
      2. Preconditions to set up the test
      3. Detailed steps to execute the test
      4. Expected results
      5. Test type (functional, integration, performance, security, usability)
      6. Priority (critical, high, medium, low)
      7. Platform (API, web, mobile)
      8. Test case type (manual, automated)
      9. Automation by AgentQ (true/false)

      Focus on:
      - Authentication and authorization testing
      - Input validation and error handling
      - Response format and status code validation
      - Edge cases and boundary conditions
      - Security vulnerabilities

      You MUST output VALID JSON exactly in this format:
      {
        "testCases": [
          {
            "name": "Test case name",
            "precondition": "Preconditions to set up the test",
            "steps": ["Step 1", "Step 2"],
            "expectation": "Expected result",
            "testType": "functional",
            "priority": "high",
            "platform": "API",
            "testCaseType": "manual",
            "automationByAgentq": false
          }
        ]
      }

      Make sure to:
      - Use double quotes for strings and property names
      - Don't use trailing commas
      - Properly close all brackets and braces
      - Format arrays with square brackets
      - Keep the exact property names as shown above
      - DO NOT include any markdown formatting, explanations, or additional text
      - ONLY return the JSON object, nothing else

      Generate 5-8 comprehensive test cases based on the following API documentation:

      ${finalContent}
    `;
    
    if (this.provider === 'OPENAI') {
      try {
        const response = await this.retryOperation(() =>
          this.openai.chat.completions.create({
          model: "gpt-4-turbo-preview",
          messages: [
            {
              role: "system",
              content: "You are a QA expert specialized in creating detailed test cases. You must provide output in valid JSON format with the exact structure requested. No markdown, no explanations, ONLY valid JSON."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          response_format: { type: "json_object" },
          temperature: 0.5, // Lower temperature for more deterministic results
        })
        );

        await this.recordTokenUsage(
          embeddingId,
          response.usage.prompt_tokens,
          response.usage.completion_tokens
        );

        // Use safe JSON parsing
        const result = this.safeJsonParse(response.choices[0].message.content);
        return result.testCases || [];
      } catch (error) {
        this.logger.error(`OpenAI error: ${error.message}`);
        return [];
      }
    } else {
      try {
        const model = this.genAI.getGenerativeModel({ 
          model: "gemini-2.0-flash",
          generationConfig: {
            responseMimeType: "application/json",
            temperature: 0.3, // Lower temperature for more reliable JSON
          }
        });

        // Enhanced system prompt for Gemini
        const systemPrompt = "You are a QA expert specialized in creating detailed test cases. You must provide output in valid JSON format with the exact structure requested. No markdown, no explanations, ONLY valid JSON.";
        
        const chat = model.startChat({
          history: [{
            role: "user",
            parts: [{ text: systemPrompt }]
          }]
        });

        const result = await this.retryOperation(() => chat.sendMessage(prompt));
        const response = await result.response;
        const text = response.text();
        
        // Estimate tokens (1 token ≈ 4 characters)
        const inputTokens = Math.ceil((systemPrompt.length + prompt.length) / 4);
        const outputTokens = Math.ceil(text.length / 4);
        await this.recordTokenUsage(embeddingId, inputTokens, outputTokens);

        // Use safe JSON parsing
        const parsedResult = this.safeJsonParse(text);
        return parsedResult.testCases || [];
      } catch (error) {
        this.logger.error(`Gemini error: ${error.message}`);
        return [];
      }
    }
  }

  async generateTestCasesWithGemini(content: string, embeddingId: string) {
    if (this.provider !== 'GEMINI') {
      return this.generateTestCases(content, embeddingId);
    }

    try {
      // Using a different approach with Gemini
      const model = this.genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
      
      // Break the prompt into multiple steps
      const initialPrompt = `As a QA expert, analyze this content and prepare to create test cases: ${content}`;
      
      const structurePrompt = `
        Now, create JSON-formatted test cases with the following structure:
        {
          "testCases": [
            {
              "name": "Test case name",
              "precondition": "Preconditions to set up the test",
              "steps": ["Step 1", "Step 2"],
              "expectation": "Expected result",
              "testType": "Type of test",
              "priority": "Priority level",
              "platform": "Platform type",
              "testCaseType": "manual",
              "automationByAgentq": false
            }
          ]
        }
        
        Make sure to:
        - Use double quotes for all strings and property names
        - Don't use trailing commas
        - Format arrays properly with square brackets
        - Include 2-4 test cases, each with 2-4 steps
        - Return ONLY the JSON, nothing else
      `;
      
      // Create a multi-turn chat
      const chat = model.startChat();
      
      // Step 1: Analyze the content
      await chat.sendMessage(initialPrompt);
      
      // Step 2: Request JSON format
      const result = await chat.sendMessage(structurePrompt);
      const response = await result.response;
      const text = response.text();
      
      // Estimate tokens
      const inputTokens = Math.ceil((initialPrompt.length + structurePrompt.length) / 4);
      const outputTokens = Math.ceil(text.length / 4);
      await this.recordTokenUsage(embeddingId, inputTokens, outputTokens);
      
      // Try to extract just the JSON part
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      const jsonText = jsonMatch ? jsonMatch[0] : text;
      
      // Use safe JSON parsing
      const parsedResult = this.safeJsonParse(jsonText);
      return parsedResult.testCases || [];
    } catch (error) {
      this.logger.error(`Alternative Gemini approach error: ${error.message}`);
      return [];
    }
  }

  async generateMoreTestCases(content: string, embeddingId: string, existingScenarios: any[]) {
    const existingScenariosText = JSON.stringify(existingScenarios, null, 2);
    
    const prompt = `
      As a QA expert, analyze the following content and generate additional comprehensive test cases.
      IMPORTANT: Generate completely new test scenarios that are different from the existing ones.

      Existing test scenarios:
      ${existingScenariosText}

      For each new test case, provide:
      1. A clear, descriptive name (must be unique)
      2. Preconditions to set up the test
      3. Detailed steps to execute the test
      4. Expected results
      5. Test type (e.g., functional, integration, performance, security, usability)
      6. Priority (e.g., critical, high, medium, low)
      7. Platform (only one platform per test case such as web, mobile, API)
      8. Test case type (default to "manual" for now)

      You MUST output VALID JSON exactly in this format:
      {
        "testCases": [
          {
            "name": "Test case name",
            "precondition": "Preconditions to set up the test",
            "steps": ["Step 1", "Step 2"],
            "expectation": "Expected result",
            "testType": "Type of test",
            "priority": "Priority level",
            "platform": "Platform type",
            "testCaseType": "Type of test case",
            "automationByAgentq": false
          }
        ]
      }

      Content to analyze:
      ${content}
    `;

    if (this.provider === 'OPENAI') {
      try {
        const response = await this.retryOperation(() =>
          this.openai.chat.completions.create({
          model: "gpt-4-turbo-preview",
          messages: [
            {
              role: "system",
              content: "You are a QA expert specialized in creating detailed test cases. Generate new, unique test cases that don't overlap with existing ones. Output in valid JSON format only."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          response_format: { type: "json_object" },
          temperature: 0.7, // Slightly higher temperature for more creative results
        })
        );

        await this.recordTokenUsage(
          embeddingId,
          response.usage.prompt_tokens,
          response.usage.completion_tokens
        );

        const result = this.safeJsonParse(response.choices[0].message.content);
        return result.testCases || [];
      } catch (error) {
        this.logger.error(`OpenAI error: ${error.message}`);
        return [];
      }
    } else {
      try {
        const model = this.genAI.getGenerativeModel({ 
          model: "gemini-2.0-flash",
          generationConfig: {
            responseMimeType: "application/json",
            temperature: 0.3,
          }
        });

        const systemPrompt = "You are a QA expert specialized in creating detailed test cases. Generate new, unique test cases that don't overlap with existing ones. Output in valid JSON format only.";
        
        const chat = model.startChat({
          history: [{
            role: "user",
            parts: [{ text: systemPrompt }]
          }]
        });

        const result = await this.retryOperation(() => chat.sendMessage(prompt));
        const response = await result.response;
        const text = response.text();
        
        const inputTokens = Math.ceil((systemPrompt.length + prompt.length) / 4);
        const outputTokens = Math.ceil(text.length / 4);
        await this.recordTokenUsage(embeddingId, inputTokens, outputTokens);

        const parsedResult = this.safeJsonParse(text);
        return parsedResult.testCases || [];
      } catch (error) {
        this.logger.error(`Gemini error: ${error.message}`);
        return [];
      }
    }
  }
}
