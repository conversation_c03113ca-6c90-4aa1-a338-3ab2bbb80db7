import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Embedding } from './embedding.entity';
import { LLMService } from './openai.service';
import { FileProcessorService } from './file-processor.service';
import * as cheerio from 'cheerio';
import * as jwt from 'atlassian-jwt';

interface EmbeddingJob {
  fileId: string;
  fileBuffer: Buffer;
  mimetype: string;
  url?: string;
  isPublishUrl?: boolean;
  integration?: string;
  integration_email?: string;
  integration_api_token?: string;
}

@Processor('embeddings')
export class EmbeddingProcessor extends WorkerHost {
  private readonly logger = new Logger(EmbeddingProcessor.name);

  constructor(
    @InjectRepository(Embedding)
    private embeddingRepository: Repository<Embedding>,
    private openAIService: LLMService,
    private fileProcessorService: FileProcessorService,
  ) {
    super();
  }

  private async processUrlContent(url: string, integration?: string, credentials?: { email: string; token: string }): Promise<string> {
    try {
      if (integration) {
        // Handle private URL (e.g., Confluence)
        return await this.fetchPrivateUrlContent(url, integration, credentials);
      }

      // Check if this is a Swagger UI URL and handle it specially
      if (this.isSwaggerUrl(url)) {
        return await this.processSwaggerUrl(url);
      }

      // Determine content type before fetching full content
      // Use GET instead of HEAD as some servers don't support HEAD properly
      let headResponse: Response;
      try {
        headResponse = await fetch(url, { method: 'HEAD' });
      } catch (headError) {
        // If HEAD fails, try GET with a small range to check content type
        this.logger.debug(`HEAD request failed for ${url}, trying GET`);
        headResponse = await fetch(url);
      }

      const contentType = headResponse.headers.get('content-type') || '';

      // Skip non-text content types
      if (!contentType.includes('text/html') &&
          !contentType.includes('text/plain') &&
          !contentType.includes('application/json')) {
        this.logger.warn(`Skipping non-text URL: ${url} (${contentType})`);
        return '';
      }

      // Handle public URL
      const response = headResponse.status === 200 ? headResponse : await fetch(url);
      const content = await response.text();

      // If it's JSON, return it directly (formatted)
      if (contentType.includes('application/json')) {
        try {
          const jsonData = JSON.parse(content);
          return JSON.stringify(jsonData, null, 2);
        } catch (jsonError) {
          this.logger.warn(`Failed to parse JSON from ${url}:`, jsonError);
          return content;
        }
      }

      return this.cleanHtmlContent(content);
    } catch (error) {
      this.logger.error(`Error processing URL ${url}:`, error);
      throw new Error(`Failed to process URL: ${error.message}`);
    }
  }

  private async cleanHtmlContent(html: string): Promise<string> {
    const cheerio = await import('cheerio');
    const $ = cheerio.load(html);
    
    // Remove common non-relevant elements
    $('script, style, noscript, iframe, video, audio, svg, canvas, footer, nav, header, aside').remove();
    $('[class*="advert"], [class*="banner"], [class*="promo"], [id*="ad-"], [class*="social"]').remove();
    $('[class*="cookie"], [class*="popup"], [class*="modal"], [class*="overlay"]').remove();
    $('[class*="animation"], [class*="slider"], [class*="carousel"]').remove();
    
    // Extract the main content
    const contentSelectors = [
      'article', 'main', '.content', '#content', '.post', '.entry', 
      '.documentation', '.docs', '.main-content', '[role="main"]'
    ];
    
    let mainContent = '';
    
    // Try to find main content container
    for (const selector of contentSelectors) {
      if ($(selector).length) {
        mainContent = $(selector).text();
        break;
      }
    }
    
    // If no main content found, extract body text with fallback
    if (!mainContent.trim()) {
      mainContent = $('body').text();
    }
    
    // Clean up the text
    let cleanedText = mainContent
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, '\n')
      .trim();
      
    // Handle empty or very short content
    if (cleanedText.length < 100) {
      this.logger.warn('Content too short after cleaning');
      const title = $('title').text();
      const description = $('meta[name="description"]').attr('content') || '';
      cleanedText = `${title}\n\n${description}`;
    }
    
    return cleanedText;
  }
  
  private async fetchPrivateUrlContent(url: string, integration: string, credentials?: { email: string; token: string }): Promise<string> {
    try {
      let content = '';

      switch (integration.toLowerCase()) {
        case 'atlassian': {
          if (!credentials?.email || !credentials?.token) {
            throw new Error('Atlassian email and API token are required');
          }

          // Determine if it's a Confluence or JIRA URL
          const isConfluence = url.includes('/wiki/') || url.includes('/pages/') || url.includes('/display/');
          const isJira = url.includes('/browse/');

          if (isConfluence) {
            // Extract page ID from Confluence URL
            const pageIdMatch = url.match(/pages\/(\d+)/);
            if (!pageIdMatch) {
              throw new Error('Invalid Confluence URL format');
            }
            const pageId = pageIdMatch[1];

            // Extract base URL (e.g., https://your-domain.atlassian.net/wiki)
            const baseUrlMatch = url.match(/^(https?:\/\/[^\/]+(?:\/wiki|\/display)?)\/?/);
            let baseUrl = baseUrlMatch ? baseUrlMatch[1] : '';
            const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

            const apiUrl = `${normalizedBaseUrl}/rest/api/content/${pageId}?expand=body.storage`;
            this.logger.debug(`Fetching Confluence content for page ID: ${pageId} from URL: ${apiUrl}`);

            const response = await fetch(apiUrl, {
              headers: {
                'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.token}`).toString('base64')}`,
                'Accept': 'application/json',
              },
            });

            if (!response.ok) {
              let errorMessage = `Confluence API error: ${response.status} ${response.statusText}`;
              if (response.status === 401) {
                errorMessage = 'Confluence API error: Unauthorized. Check your credentials.';
              } else if (response.status === 404) {
                errorMessage = 'Confluence API error: Page not found.';
              } else if (response.status === 500) {
                errorMessage = 'Confluence API error: Internal server error.';
              }
              this.logger.error(errorMessage);
              throw new Error(errorMessage);
            }

            const data = await response.json();
            
            if (!data.body?.storage?.value) {
              throw new Error('No content found in Confluence response');
            }

            // Convert Confluence storage format (HTML) to plain text
            const $ = cheerio.load(data.body.storage.value);
            content = $.text();

            // Clean up the content
            content = content
              .replace(/\s+/g, ' ')
              .replace(/\n+/g, '\n')
              .trim();

            this.logger.debug(`Successfully fetched Confluence content for page ${pageId}`);
          } else if (isJira) {
            // Extract issue key from JIRA URL
            const issueKeyMatch = url.match(/\/browse\/([A-Z]+-\d+)/);
            if (!issueKeyMatch) {
              throw new Error('Invalid JIRA URL format');
            }
            const issueKey = issueKeyMatch[1];

            // Extract base URL (e.g., https://your-domain.atlassian.net)
            const baseUrlMatch = url.match(/^(https?:\/\/[^\/]+)/);
            if (!baseUrlMatch) {
              throw new Error('Invalid JIRA URL format');
            }
            const baseUrl = baseUrlMatch[1];

            // Fetch issue details using JIRA REST API
            const apiUrl = `${baseUrl}/rest/api/2/issue/${issueKey}?fields=summary,description,attachment,customfield_10000`;
            this.logger.debug(`Fetching JIRA content for issue: ${issueKey} from URL: ${apiUrl}`);

            const response = await fetch(apiUrl, {
              headers: {
                'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.token}`).toString('base64')}`,
                'Accept': 'application/json',
              },
            });

            if (!response.ok) {
              let errorMessage = `JIRA API error: ${response.status} ${response.statusText}`;
              if (response.status === 401) {
                errorMessage = 'JIRA API error: Unauthorized. Check your credentials.';
              } else if (response.status === 404) {
                errorMessage = 'JIRA API error: Issue not found.';
              }
              this.logger.error(errorMessage);
              throw new Error(errorMessage);
            }

            const data = await response.json();
            
            // Extract relevant fields
            const summary = data.fields.summary || '';
            const description = data.fields.description || '';
            
            // Try to get acceptance criteria (often stored in customfield_10000 as "Acceptance Criteria")
            // Note: The actual field ID may vary between JIRA instances
            let acceptanceCriteria = '';
            if (data.fields.customfield_10000) {
              acceptanceCriteria = data.fields.customfield_10000;
            }
            
            // Combine the content
            content = `JIRA Issue: ${issueKey}\n\nSummary: ${summary}\n\nDescription: ${description}\n\nAcceptance Criteria: ${acceptanceCriteria}`;
            
            // Handle attachments if available
            if (data.fields.attachment && data.fields.attachment.length > 0) {
              // Get the first few attachments (limit to avoid overloading)
              const attachmentsToProcess = data.fields.attachment.slice(0, 3);
              
              for (const attachment of attachmentsToProcess) {
                if (this.isTextBasedAttachment(attachment.filename)) {
                  try {
                    // Download attachment content
                    const attachmentResponse = await fetch(attachment.content, {
                      headers: {
                        'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.token}`).toString('base64')}`,
                      },
                    });
                    
                    if (attachmentResponse.ok) {
                      const attachmentContent = await attachmentResponse.text();
                      content += `\n\nAttachment (${attachment.filename}):\n${attachmentContent}`;
                    }
                  } catch (attachmentError) {
                    this.logger.warn(`Failed to fetch attachment ${attachment.filename}: ${attachmentError.message}`);
                  }
                }
              }
            }
            
            this.logger.debug(`Successfully fetched JIRA content for issue ${issueKey}`);
          } else {
            throw new Error('Unsupported Atlassian URL format. Must be a Confluence or JIRA URL.');
          }
          break;
        }

        case 'sharepoint':
          // Implement SharePoint API call here
          this.logger.debug(`Fetching content from SharePoint URL: ${url}`);
          throw new Error('SharePoint integration not implemented yet');

        default:
          throw new Error(`Unsupported integration: ${integration}`);
      }

      if (!content) {
        throw new Error(`Failed to fetch content from private URL: ${url}`);
      }

      return content;
    } catch (error) {
      this.logger.error(`Error fetching private URL content: ${url}`, error);
      throw error;
    }
  }

  // Helper method to determine if an attachment is text-based
  private isTextBasedAttachment(filename: string): boolean {
    const textExtensions = ['.txt', '.md', '.json', '.csv', '.xml', '.html', '.js', '.ts', '.py', '.java', '.c', '.cpp', '.h', '.cs'];
    const extension = filename.substring(filename.lastIndexOf('.')).toLowerCase();
    return textExtensions.includes(extension);
  }

  // Helper method to detect Swagger UI URLs
  private isSwaggerUrl(url: string): boolean {
    const swaggerPatterns = [
      /\/swagger\//i,
      /\/swagger-ui\//i,
      /\/api-docs\//i,
      /\/docs\//i,
      /swagger.*\.html/i,
      /swagger.*\.json/i,
      /openapi.*\.json/i
    ];

    return swaggerPatterns.some(pattern => pattern.test(url));
  }

  // Process Swagger UI URLs by finding and fetching the actual API documentation
  private async processSwaggerUrl(url: string): Promise<string> {
    try {
      this.logger.debug(`Processing Swagger URL: ${url}`);

      // First, try to fetch the HTML to extract API spec URLs
      const response = await fetch(url);
      const html = await response.text();

      // Parse the HTML to find API spec URLs
      const apiUrls = this.extractSwaggerApiUrls(html, url);

      if (apiUrls.length === 0) {
        this.logger.warn(`No API spec URLs found in Swagger UI: ${url}`);
        // Fallback to cleaning the HTML content
        return this.cleanHtmlContent(html);
      }

      // Fetch all API specifications
      const apiContents: string[] = [];

      for (const apiUrl of apiUrls) {
        try {
          this.logger.debug(`Fetching API spec from: ${apiUrl}`);
          const apiResponse = await fetch(apiUrl);

          if (!apiResponse.ok) {
            this.logger.warn(`Failed to fetch API spec from ${apiUrl}: ${apiResponse.status}`);
            continue;
          }

          const apiContent = await apiResponse.text();

          // Try to parse as JSON and format it nicely
          try {
            const apiJson = JSON.parse(apiContent);
            const formattedContent = this.formatSwaggerJson(apiJson);
            apiContents.push(formattedContent);
          } catch (jsonError) {
            // If not JSON, use as-is
            apiContents.push(apiContent);
          }
        } catch (fetchError) {
          this.logger.warn(`Error fetching API spec from ${apiUrl}:`, fetchError);
        }
      }

      if (apiContents.length === 0) {
        this.logger.warn(`Failed to fetch any API specifications from ${url}`);
        // Fallback to cleaning the HTML content
        return this.cleanHtmlContent(html);
      }

      // Combine all API specifications
      const combinedContent = apiContents.join('\n\n---\n\n');
      this.logger.debug(`Successfully processed Swagger URL with ${apiContents.length} API specs`);
      this.logger.debug(`Combined content length: ${combinedContent.length} characters`);
      this.logger.debug(`First 500 characters: ${combinedContent.substring(0, 500)}`);

      return combinedContent;
    } catch (error) {
      this.logger.error(`Error processing Swagger URL ${url}:`, error);
      // Fallback to regular HTML processing
      const response = await fetch(url);
      const html = await response.text();
      return this.cleanHtmlContent(html);
    }
  }

  // Extract API specification URLs from Swagger UI HTML
  private extractSwaggerApiUrls(html: string, baseUrl: string): string[] {
    const urls: string[] = [];

    try {
      // Parse the base URL to get the origin and path
      const urlObj = new URL(baseUrl);
      const baseOrigin = urlObj.origin;
      const basePath = urlObj.pathname.replace(/\/[^\/]*$/, ''); // Remove filename

      // Look for common Swagger UI patterns
      const patterns = [
        // Look for urls array in JavaScript
        /urls:\s*\[\s*([^\]]+)\]/g,
        // Look for url property
        /url:\s*["']([^"']+)["']/g,
        // Look for spec URLs in data attributes
        /data-spec-url=["']([^"']+)["']/g,
      ];

      for (const pattern of patterns) {
        let match: RegExpExecArray | null;
        while ((match = pattern.exec(html)) !== null) {
          if (match[1]) {
            // Handle urls array format
            if (match[0].includes('urls:')) {
              const urlsContent = match[1];
              const urlMatches = urlsContent.match(/url:\s*["']([^"']+)["']/g);
              if (urlMatches) {
                for (const urlMatch of urlMatches) {
                  const urlValue = urlMatch.match(/["']([^"']+)["']/)?.[1];
                  if (urlValue) {
                    urls.push(this.resolveUrl(urlValue, baseOrigin, basePath));
                  }
                }
              }
            } else {
              // Handle single url format
              urls.push(this.resolveUrl(match[1], baseOrigin, basePath));
            }
          }
        }
      }

      // If no URLs found, try common default paths
      if (urls.length === 0) {
        const commonPaths = [
          '/swagger.json',
          '/swagger.yaml',
          '/openapi.json',
          '/openapi.yaml',
          '/api-docs',
          '/v1/swagger.json',
          '/v2/swagger.json',
          '/docs/swagger.json',
          '/api/swagger.json',
          '/swagger/v1',
          '/swagger/v2',
          '/api/swagger/v1',
          '/api/swagger/v2'
        ];

        for (const path of commonPaths) {
          urls.push(baseOrigin + path);
        }
      }

      // Remove duplicates
      return [...new Set(urls)];
    } catch (error) {
      this.logger.warn(`Error extracting Swagger API URLs from ${baseUrl}:`, error);
      return [];
    }
  }

  // Resolve relative URLs to absolute URLs
  private resolveUrl(url: string, baseOrigin: string, basePath: string): string {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    if (url.startsWith('/')) {
      return baseOrigin + url;
    }

    return baseOrigin + basePath + '/' + url;
  }

  // Format Swagger JSON into readable text for embedding
  private formatSwaggerJson(swaggerJson: any): string {
    try {
      const parts: string[] = [];

      // Add basic info
      if (swaggerJson.info) {
        parts.push(`API: ${swaggerJson.info.title || 'Unknown'}`);
        if (swaggerJson.info.description) {
          parts.push(`Description: ${swaggerJson.info.description}`);
        }
        if (swaggerJson.info.version) {
          parts.push(`Version: ${swaggerJson.info.version}`);
        }
      }

      // Add server information
      if (swaggerJson.servers && swaggerJson.servers.length > 0) {
        parts.push('\nServers:');
        swaggerJson.servers.forEach((server: any) => {
          parts.push(`- ${server.url}${server.description ? ': ' + server.description : ''}`);
        });
      } else if (swaggerJson.host) {
        parts.push(`\nHost: ${swaggerJson.host}`);
        if (swaggerJson.basePath) {
          parts.push(`Base Path: ${swaggerJson.basePath}`);
        }
      }

      // Add security schemes
      if (swaggerJson.securityDefinitions || swaggerJson.components?.securitySchemes) {
        const securitySchemes = swaggerJson.securityDefinitions || swaggerJson.components?.securitySchemes;
        parts.push('\nSecurity:');
        Object.entries(securitySchemes).forEach(([name, scheme]: [string, any]) => {
          parts.push(`- ${name}: ${scheme.type} ${scheme.description ? '- ' + scheme.description : ''}`);
        });
      }

      // Add paths/endpoints with detailed information
      if (swaggerJson.paths) {
        parts.push('\nAPI Endpoints:');

        Object.entries(swaggerJson.paths).forEach(([path, pathObj]: [string, any]) => {
          Object.entries(pathObj).forEach(([method, operation]: [string, any]) => {
            if (typeof operation === 'object' && operation !== null) {
              const summary = operation.summary || '';
              const description = operation.description || '';
              parts.push(`\n${method.toUpperCase()} ${path}`);
              if (summary) parts.push(`  Summary: ${summary}`);
              if (description) parts.push(`  Description: ${description}`);

              // Add tags
              if (operation.tags && operation.tags.length > 0) {
                parts.push(`  Tags: ${operation.tags.join(', ')}`);
              }

              // Add security requirements
              if (operation.security && operation.security.length > 0) {
                parts.push('  Security:');
                operation.security.forEach((sec: any) => {
                  Object.keys(sec).forEach(secName => {
                    parts.push(`    - ${secName}: ${sec[secName].join(', ') || 'required'}`);
                  });
                });
              }

              // Add consumes/produces
              if (operation.consumes && operation.consumes.length > 0) {
                parts.push(`  Consumes: ${operation.consumes.join(', ')}`);
              }
              if (operation.produces && operation.produces.length > 0) {
                parts.push(`  Produces: ${operation.produces.join(', ')}`);
              }

              // Add parameters with detailed information
              if (operation.parameters && operation.parameters.length > 0) {
                parts.push('  Parameters:');
                operation.parameters.forEach((param: any) => {
                  const paramInfo = [`${param.name} (${param.in})`];
                  if (param.type) paramInfo.push(`type: ${param.type}`);
                  if (param.required) paramInfo.push('required');
                  if (param.description) paramInfo.push(`description: ${param.description}`);
                  if (param.example !== undefined) paramInfo.push(`example: ${JSON.stringify(param.example)}`);
                  if (param.default !== undefined) paramInfo.push(`default: ${JSON.stringify(param.default)}`);
                  if (param.enum) paramInfo.push(`enum: [${param.enum.join(', ')}]`);
                  parts.push(`    - ${paramInfo.join(', ')}`);
                });
              }

              // Add request body schema (OpenAPI 3.0)
              if (operation.requestBody) {
                parts.push('  Request Body:');
                if (operation.requestBody.description) {
                  parts.push(`    Description: ${operation.requestBody.description}`);
                }
                if (operation.requestBody.content) {
                  Object.entries(operation.requestBody.content).forEach(([mediaType, content]: [string, any]) => {
                    parts.push(`    ${mediaType}:`);
                    if (content.schema) {
                      const schemaInfo = this.formatSchemaInfo(content.schema, swaggerJson);
                      parts.push(`      Schema: ${schemaInfo}`);
                    }
                    if (content.example) {
                      parts.push(`      Example: ${JSON.stringify(content.example, null, 2)}`);
                    }
                  });
                }
              }

              // Add responses with comprehensive schema information and examples
              if (operation.responses) {
                parts.push('  Responses:');
                Object.entries(operation.responses).forEach(([code, response]: [string, any]) => {
                  parts.push(`    ${code}: ${response.description || 'No description'}`);

                  // Add response schema information (Swagger 2.0)
                  if (response.schema) {
                    const detailedSchema = this.formatDetailedSchemaWithExamples(response.schema, swaggerJson, 0);
                    parts.push(`      Schema: ${detailedSchema}`);
                  }

                  // Add response content (OpenAPI 3.0)
                  if (response.content) {
                    Object.entries(response.content).forEach(([mediaType, content]: [string, any]) => {
                      parts.push(`      Content-Type: ${mediaType}`);
                      if (content.schema) {
                        const detailedSchema = this.formatDetailedSchemaWithExamples(content.schema, swaggerJson, 0);
                        parts.push(`        Schema: ${detailedSchema}`);
                      }
                      if (content.example) {
                        parts.push(`        Response Example:`);
                        parts.push(`${JSON.stringify(content.example, null, 2)}`);
                      }
                      if (content.examples) {
                        parts.push(`        Response Examples:`);
                        Object.entries(content.examples).forEach(([exampleName, example]: [string, any]) => {
                          parts.push(`          ${exampleName}:`);
                          if (example.value) {
                            parts.push(`${JSON.stringify(example.value, null, 2)}`);
                          }
                        });
                      }
                    });
                  }

                  // Add examples (Swagger 2.0)
                  if (response.examples) {
                    parts.push('      Response Examples:');
                    Object.entries(response.examples).forEach(([exampleType, example]: [string, any]) => {
                      parts.push(`        ${exampleType}:`);
                      parts.push(`${JSON.stringify(example, null, 2)}`);
                    });
                  }
                });
              }
            }
          });
        });
      }

      // Add detailed schema definitions with full expansion
      if (swaggerJson.components?.schemas || swaggerJson.definitions) {
        const schemas = swaggerJson.components?.schemas || swaggerJson.definitions;
        parts.push('\nDetailed Data Models and Schemas:');

        // Sort schemas to prioritize commonly used ones
        const sortedSchemas = Object.entries(schemas).sort(([a], [b]) => {
          // Prioritize Response, Error, and common schemas
          const priority = (name: string) => {
            if (name.toLowerCase().includes('response')) return 1;
            if (name.toLowerCase().includes('error')) return 2;
            if (name.toLowerCase().includes('result')) return 3;
            if (name.toLowerCase().includes('data')) return 4;
            return 5;
          };
          return priority(a) - priority(b);
        });

        sortedSchemas.forEach(([name, schema]: [string, any]) => {
          parts.push(`\n=== ${name} Schema ===`);

          if (schema.description) {
            parts.push(`Description: ${schema.description}`);
          }

          // Add type information
          if (schema.type) {
            parts.push(`Type: ${schema.type}`);
          }

          // Add properties with full detailed information
          if (schema.properties) {
            parts.push('Properties:');
            Object.entries(schema.properties).forEach(([propName, prop]: [string, any]) => {
              const required = schema.required && schema.required.includes(propName) ? ' (REQUIRED)' : ' (optional)';
              parts.push(`  • ${propName}${required}:`);

              // Get detailed property information
              const propDetails = this.getDetailedPropertyInfo(prop, swaggerJson);
              propDetails.forEach(detail => parts.push(`    ${detail}`));
            });
          }

          // Add example if available with proper formatting
          if (schema.example) {
            parts.push('Example:');
            parts.push(`${JSON.stringify(schema.example, null, 2)}`);
          }

          // Add enum values
          if (schema.enum) {
            parts.push(`Possible values: ${schema.enum.join(', ')}`);
          }

          // Handle allOf, oneOf, anyOf in definitions with full expansion
          if (schema.allOf) {
            parts.push('Composed of (allOf):');
            schema.allOf.forEach((subSchema: any, index: number) => {
              if (subSchema.$ref) {
                const refSchema = this.resolveSchemaRef(subSchema.$ref, swaggerJson);
                const refName = subSchema.$ref.split('/').pop();
                parts.push(`  ${index + 1}. Inherits from ${refName}:`);
                if (refSchema && refSchema.properties) {
                  Object.entries(refSchema.properties).forEach(([propName, prop]: [string, any]) => {
                    const propDetails = this.getDetailedPropertyInfo(prop, swaggerJson);
                    parts.push(`    • ${propName}: ${propDetails.join(', ')}`);
                  });
                }
              } else {
                const subInfo = this.formatSchemaInfo(subSchema, swaggerJson, 1);
                parts.push(`  ${index + 1}. ${subInfo}`);
              }
            });
          }

          if (schema.oneOf) {
            parts.push('One of (oneOf):');
            schema.oneOf.forEach((subSchema: any, index: number) => {
              const subInfo = this.formatSchemaInfo(subSchema, swaggerJson, 1);
              parts.push(`  ${index + 1}. ${subInfo}`);
            });
          }

          // Add validation constraints
          const constraints: string[] = [];
          if (schema.minimum !== undefined) constraints.push(`minimum: ${schema.minimum}`);
          if (schema.maximum !== undefined) constraints.push(`maximum: ${schema.maximum}`);
          if (schema.minLength !== undefined) constraints.push(`minLength: ${schema.minLength}`);
          if (schema.maxLength !== undefined) constraints.push(`maxLength: ${schema.maxLength}`);
          if (schema.pattern) constraints.push(`pattern: ${schema.pattern}`);
          if (constraints.length > 0) {
            parts.push(`Constraints: ${constraints.join(', ')}`);
          }
        });
      }

      return parts.join('\n');
    } catch (error) {
      this.logger.warn('Error formatting Swagger JSON:', error);
      return JSON.stringify(swaggerJson, null, 2);
    }
  }

  // Helper method to resolve schema references and get full schema definition
  private resolveSchemaRef(ref: string, swaggerJson: any): any {
    const refPath = ref.replace('#/', '').split('/');
    let refSchema = swaggerJson;
    for (const part of refPath) {
      refSchema = refSchema[part];
      if (!refSchema) break;
    }
    return refSchema;
  }

  // Helper method to get detailed property information
  private getDetailedPropertyInfo(prop: any, swaggerJson: any): string[] {
    const details: string[] = [];

    // Handle $ref references
    if (prop.$ref) {
      const refSchema = this.resolveSchemaRef(prop.$ref, swaggerJson);
      const refName = prop.$ref.split('/').pop();
      if (refSchema) {
        details.push(`Type: ${refName} (${refSchema.type || 'object'})`);
        if (refSchema.description) {
          details.push(`Description: ${refSchema.description}`);
        }
        if (refSchema.example) {
          details.push(`Example: ${JSON.stringify(refSchema.example)}`);
        }
      } else {
        details.push(`Type: ${refName} (reference)`);
      }
      return details;
    }

    // Add type information
    if (prop.type) {
      details.push(`Type: ${prop.type}`);
    }

    // Add format information
    if (prop.format) {
      details.push(`Format: ${prop.format}`);
    }

    // Add description
    if (prop.description) {
      details.push(`Description: ${prop.description}`);
    }

    // Add example
    if (prop.example !== undefined) {
      if (typeof prop.example === 'object') {
        details.push(`Example: ${JSON.stringify(prop.example)}`);
      } else {
        details.push(`Example: ${prop.example}`);
      }
    }

    // Add enum values
    if (prop.enum) {
      details.push(`Enum: [${prop.enum.join(', ')}]`);
    }

    // Add validation constraints
    if (prop.minimum !== undefined) details.push(`Min: ${prop.minimum}`);
    if (prop.maximum !== undefined) details.push(`Max: ${prop.maximum}`);
    if (prop.minLength !== undefined) details.push(`MinLength: ${prop.minLength}`);
    if (prop.maxLength !== undefined) details.push(`MaxLength: ${prop.maxLength}`);
    if (prop.pattern) details.push(`Pattern: ${prop.pattern}`);

    // Handle array items
    if (prop.type === 'array' && prop.items) {
      if (prop.items.$ref) {
        const refName = prop.items.$ref.split('/').pop();
        details.push(`Items: Array of ${refName}`);
      } else {
        details.push(`Items: Array of ${prop.items.type || 'unknown'}`);
      }
    }

    // Handle object properties (limited to avoid too much nesting)
    if (prop.type === 'object' && prop.properties) {
      const propCount = Object.keys(prop.properties).length;
      details.push(`Properties: Object with ${propCount} properties`);
    }

    return details.length > 0 ? details : ['Type: unknown'];
  }

  // Helper method to format detailed schema with examples and full object expansion
  private formatDetailedSchemaWithExamples(schema: any, swaggerJson: any, depth: number = 0): string {
    if (depth > 3) return '[nested object]';

    try {
      // Handle $ref references - resolve them fully with examples
      if (schema.$ref) {
        const refSchema = this.resolveSchemaRef(schema.$ref, swaggerJson);
        if (refSchema) {
          const refName = schema.$ref.split('/').pop();

          // For top-level schemas, show full details
          if (depth === 0) {
            return this.formatCompleteSchemaStructure(refSchema, swaggerJson, refName);
          } else {
            return `${refName} (${refSchema.type || 'object'})`;
          }
        }
        return schema.$ref;
      }

      // For object schemas, show complete structure
      if (schema.type === 'object' || (!schema.type && schema.properties)) {
        return this.formatCompleteSchemaStructure(schema, swaggerJson);
      }

      // For other types, use the existing detailed formatting
      return this.formatSchemaInfo(schema, swaggerJson, depth);
    } catch (error) {
      return 'schema parsing error';
    }
  }

  // Helper method to format complete schema structure with all properties
  private formatCompleteSchemaStructure(schema: any, swaggerJson: any, schemaName?: string): string {
    const parts: string[] = [];

    if (schemaName) {
      parts.push(`${schemaName} {`);
    } else {
      parts.push(`Object {`);
    }

    // Add description
    if (schema.description) {
      parts.push(`  // ${schema.description}`);
    }

    // Add all properties with detailed information
    if (schema.properties) {
      Object.entries(schema.properties).forEach(([propName, propSchema]: [string, any]) => {
        const required = schema.required && schema.required.includes(propName) ? ' (required)' : '';
        const propDetails = this.formatPropertyWithType(propSchema, swaggerJson);
        parts.push(`  ${propName}${required}: ${propDetails}`);
      });
    }

    // Handle allOf compositions
    if (schema.allOf) {
      schema.allOf.forEach((subSchema: any) => {
        if (subSchema.$ref) {
          const refSchema = this.resolveSchemaRef(subSchema.$ref, swaggerJson);
          const refName = subSchema.$ref.split('/').pop();
          parts.push(`  // Inherits from ${refName}`);
          if (refSchema && refSchema.properties) {
            Object.entries(refSchema.properties).forEach(([propName, propSchema]: [string, any]) => {
              const propDetails = this.formatPropertyWithType(propSchema, swaggerJson);
              parts.push(`  ${propName}: ${propDetails}`);
            });
          }
        } else if (subSchema.properties) {
          Object.entries(subSchema.properties).forEach(([propName, propSchema]: [string, any]) => {
            const propDetails = this.formatPropertyWithType(propSchema, swaggerJson);
            parts.push(`  ${propName}: ${propDetails}`);
          });
        }
      });
    }

    parts.push(`}`);

    // Add example if available
    if (schema.example) {
      parts.push(`Example: ${JSON.stringify(schema.example, null, 2)}`);
    }

    return parts.join('\n');
  }

  // Helper method to format property with type information
  private formatPropertyWithType(propSchema: any, swaggerJson: any): string {
    if (propSchema.$ref) {
      const refSchema = this.resolveSchemaRef(propSchema.$ref, swaggerJson);
      const refName = propSchema.$ref.split('/').pop();
      if (refSchema) {
        if (refSchema.type === 'object' && refSchema.properties) {
          // For object references, show the structure
          const propCount = Object.keys(refSchema.properties).length;
          return `${refName} (object with ${propCount} properties)`;
        } else {
          return `${refName} (${refSchema.type || 'object'})`;
        }
      }
      return refName;
    }

    const parts: string[] = [];

    // Add type
    if (propSchema.type) {
      parts.push(propSchema.type);
    }

    // Add format
    if (propSchema.format) {
      parts.push(`(${propSchema.format})`);
    }

    // Add description
    if (propSchema.description) {
      parts.push(`// ${propSchema.description}`);
    }

    // Add example
    if (propSchema.example !== undefined) {
      parts.push(`example: ${JSON.stringify(propSchema.example)}`);
    }

    // Add enum values
    if (propSchema.enum) {
      parts.push(`enum: [${propSchema.enum.join(', ')}]`);
    }

    // Handle arrays
    if (propSchema.type === 'array' && propSchema.items) {
      const itemType = this.formatPropertyWithType(propSchema.items, swaggerJson);
      return `array of ${itemType}`;
    }

    // Handle nested objects (limited depth)
    if (propSchema.type === 'object' && propSchema.properties) {
      const propCount = Object.keys(propSchema.properties).length;
      parts.push(`object with ${propCount} properties`);
    }

    return parts.join(' ');
  }

  // Helper method to format schema information with full reference resolution and examples
  private formatSchemaInfo(schema: any, swaggerJson: any, depth: number = 0): string {
    if (depth > 4) return '[nested object]'; // Prevent infinite recursion

    try {
      // Handle $ref references - resolve them fully
      if (schema.$ref) {
        const refSchema = this.resolveSchemaRef(schema.$ref, swaggerJson);
        if (refSchema) {
          const refName = schema.$ref.split('/').pop();

          // For shallow depth, show detailed schema info
          if (depth < 2) {
            const refInfo = this.formatSchemaInfo(refSchema, swaggerJson, depth + 1);
            return `${refName}: ${refInfo}`;
          } else {
            // For deeper levels, just show the reference name and basic type
            return `${refName} (${refSchema.type || 'object'})`;
          }
        }
        return schema.$ref;
      }

      const parts: string[] = [];

      // Add type information
      if (schema.type) {
        parts.push(`type: ${schema.type}`);
      }

      // Add format information
      if (schema.format) {
        parts.push(`format: ${schema.format}`);
      }

      // Add description
      if (schema.description) {
        parts.push(`description: "${schema.description}"`);
      }

      // Add example with proper formatting
      if (schema.example !== undefined) {
        if (typeof schema.example === 'object') {
          parts.push(`example: ${JSON.stringify(schema.example, null, 2)}`);
        } else {
          parts.push(`example: ${schema.example}`);
        }
      }

      // Add enum values
      if (schema.enum) {
        parts.push(`enum: [${schema.enum.join(', ')}]`);
      }

      // Add array item information
      if (schema.type === 'array' && schema.items) {
        const itemInfo = this.formatSchemaInfo(schema.items, swaggerJson, depth + 1);
        parts.push(`items: [${itemInfo}]`);
      }

      // Add object properties with full details
      if (schema.type === 'object' && schema.properties && depth < 3) {
        const propParts: string[] = [];
        Object.entries(schema.properties).forEach(([propName, propSchema]: [string, any]) => {
          const propInfo = this.formatSchemaInfo(propSchema, swaggerJson, depth + 1);
          const required = schema.required && schema.required.includes(propName) ? ' (required)' : '';
          propParts.push(`${propName}: ${propInfo}${required}`);
        });
        if (propParts.length > 0) {
          parts.push(`properties: {${propParts.join(', ')}}`);
        }
      }

      // Handle allOf with full resolution
      if (schema.allOf) {
        const allOfParts: string[] = [];
        schema.allOf.forEach((subSchema: any, index: number) => {
          const subInfo = this.formatSchemaInfo(subSchema, swaggerJson, depth + 1);
          allOfParts.push(`${index + 1}. ${subInfo}`);
        });
        parts.push(`allOf: [${allOfParts.join(', ')}]`);
      }

      // Handle oneOf with full resolution
      if (schema.oneOf) {
        const oneOfParts: string[] = [];
        schema.oneOf.forEach((subSchema: any, index: number) => {
          const subInfo = this.formatSchemaInfo(subSchema, swaggerJson, depth + 1);
          oneOfParts.push(`${index + 1}. ${subInfo}`);
        });
        parts.push(`oneOf: [${oneOfParts.join(', ')}]`);
      }

      // Handle anyOf with full resolution
      if (schema.anyOf) {
        const anyOfParts: string[] = [];
        schema.anyOf.forEach((subSchema: any, index: number) => {
          const subInfo = this.formatSchemaInfo(subSchema, swaggerJson, depth + 1);
          anyOfParts.push(`${index + 1}. ${subInfo}`);
        });
        parts.push(`anyOf: [${anyOfParts.join(', ')}]`);
      }

      // Add validation constraints
      if (schema.minimum !== undefined) parts.push(`min: ${schema.minimum}`);
      if (schema.maximum !== undefined) parts.push(`max: ${schema.maximum}`);
      if (schema.minLength !== undefined) parts.push(`minLength: ${schema.minLength}`);
      if (schema.maxLength !== undefined) parts.push(`maxLength: ${schema.maxLength}`);
      if (schema.pattern) parts.push(`pattern: ${schema.pattern}`);
      if (schema.minItems !== undefined) parts.push(`minItems: ${schema.minItems}`);
      if (schema.maxItems !== undefined) parts.push(`maxItems: ${schema.maxItems}`);

      return parts.length > 0 ? parts.join(', ') : 'unknown';
    } catch (error) {
      return 'schema parsing error';
    }
  }

  async process(job: Job<EmbeddingJob>): Promise<void> {
    this.logger.debug(`Processing embedding job ${job.id}`);

    try {
      let text: string;

      // Extract text based on job type (file or URL)
      if (job.data.fileBuffer && job.data.mimetype) {
        // Process file
        text = await this.fileProcessorService.extractText({
          buffer: Buffer.from(job.data.fileBuffer),
          mimetype: job.data.mimetype,
        } as Express.Multer.File);
      } else if (job.data.url) {
        // Fetch and process URL content
        text = await this.processUrlContent(
          job.data.url,
          job.data.integration,
          job.data.integration_email && job.data.integration_api_token
            ? {
                email: job.data.integration_email,
                token: job.data.integration_api_token,
              }
            : undefined
        );
      } else {
        throw new Error('Invalid job data: requires either file or URL');
      }

      // Split text into chunks
      const chunks = this.fileProcessorService.splitIntoChunks(text);

      // Process chunks in batches to avoid rate limits
      const batchSize = 20;
      for (let i = 0; i < chunks.length; i += batchSize) {
        const batchChunks = chunks.slice(i, i + batchSize);
        
        // Create the embedding entities with correct ID field
        const embeddingEntities = batchChunks.map(content => ({
          content,
          embedding: [], // Will be updated after OpenAI call
          ...(job.data.isPublishUrl 
            ? { publishUrlId: job.data.fileId }
            : { fileUploadId: job.data.fileId }
          ),
        }));

        // Save the embedding entities first to get their IDs
        const savedEmbeddings = await this.embeddingRepository.save(embeddingEntities);
        
        // Create embeddings for the batch using all embedding IDs for token tracking
        const embeddings = await this.openAIService.createEmbeddings(
          batchChunks,
          savedEmbeddings.map(e => e.id)
        );

        // Update the embeddings with their vector data
        await Promise.all(
          savedEmbeddings.map((entity, index) =>
            this.embeddingRepository.update(entity.id, {
              embedding: embeddings[index]
            })
          )
        );

        // Update job progress
        await job.updateProgress((i + batchSize) / chunks.length * 100);

        // Add a small delay between batches to respect rate limits
        if (i + batchSize < chunks.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      this.logger.debug(`Completed embedding job ${job.id}`);
    } catch (error) {
      this.logger.error(`Error processing embedding job ${job.id}:`, error);
      throw error;
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.debug(`Job ${job.id} completed successfully`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, error: Error) {
    this.logger.error(`Job ${job.id} failed:`, error);
  }
}
